#pragma once
#include "../../gamedata.hpp"
#include "../../globals.hpp"
#include "../../bones.hpp"
#include "../../../render/render.hpp"
#include "../../../math/vector.hpp"
#include "../../entity.hpp"

#include <thread>
#include <chrono>
#include <Windows.h>

// Include the professional mouse system
#include "mouse.h"

// Vec2 structure for 2D positions
struct Vec2 {
    float x, y;
    Vec2() : x(0), y(0) {}
    Vec2(float x, float y) : x(x), y(y) {}
};

class Aimbot
{
public:
	void doAimbot(const Reader& reader);
	bool Initialize(); // Initialize mouse system
	void Shutdown();   // Cleanup mouse system

private:
	float DistanceBetweenCross(float X, float Y);
	Vec2 GetClosestPlayerToCrosshair(const std::vector<Vector> playerPositions, float &Max);
	void AimAtPos(Vec2 Pos);
	float CalculateNorm(Vec2 screenPos, Vec2 center);

	// Screen center coordinates
	float ScreenCenterX = static_cast<float>(GetSystemMetrics(SM_CXSCREEN)) / 2.0f;
	float ScreenCenterY = static_cast<float>(GetSystemMetrics(SM_CYSCREEN)) / 2.0f;

	// Aimbot settings
	bool Smooth = true;
	float AimFov = 100.0f; // FOV for aiming

	// Target persistence to reduce jittering
	Vec2 lastTarget = Vec2(0, 0);
	int targetLockFrames = 0;

	// Mouse system status
	bool mouseInitialized = false;
};

inline Aimbot aimbot;