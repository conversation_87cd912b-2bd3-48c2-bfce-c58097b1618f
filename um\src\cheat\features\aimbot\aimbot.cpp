#include "aimbot.hpp"
#include "../../entity.hpp"
#include "../../globals.hpp"
#include "../../gamedata.hpp"
#include <vector>
#include <cmath>
#include <Windows.h>

// ===========================
// AIMBOT IMPLEMENTATION
// ===========================

void Aimbot::doAimbot(const Reader& reader)
{
	view_matrix_t viewMatrix = GameData::getViewMatrix();
	auto playerList = reader.getPlayerListCopy();

	std::vector<Vector> playerPositions;
	playerPositions.clear();

	for (const auto& player : playerList)
	{
		// get the 3D position of the player we're CURRENTLY looping through.
		Vector playerPosition = driver::read_memory<Vector>(GameVars::getInstance()->getDriver(), player.BoneArray + bones::head * 32);


		//if (!player.enemySpotted && player.playerTeam != localTeam && Legitbot.visiblecheck)
		//	continue;

		int localTeam = GameData::getLocalTeam();
		if (player.team == localTeam && globals::Legitbot::teamcheck)
			continue;


		Vector h;
		if (Vector::world_to_screen(viewMatrix, playerPosition, h))
		{
			playerPositions.push_back(h);
		}
	}

	if (GetAsyncKeyState(0x58)) // X on keyboard
	{
		float maxDistance = globals::Legitbot::radius;
		Vec2 closestPlayer = GetClosestPlayerToCrosshair(playerPositions, maxDistance);

		// Ziel-Persistenz: Wenn wir ein gültiges Ziel haben
		if (closestPlayer.x != 0 || closestPlayer.y != 0)
		{
			// Prüfe ob es das gleiche Ziel wie vorher ist (verhindert Zittern)
			float distanceToLastTarget = sqrt(pow(closestPlayer.x - lastTarget.x, 2) + pow(closestPlayer.y - lastTarget.y, 2));

			if (distanceToLastTarget < 10.0f) {
				// Gleiches Ziel - erhöhe Lock-Counter
				targetLockFrames++;
			} else {
				// Neues Ziel - reset Counter
				targetLockFrames = 0;
				lastTarget = closestPlayer;
			}

			// Nur zielen wenn wir das Ziel für mindestens 2 Frames haben (reduziert Flackern)
			if (targetLockFrames >= 1) {
				AimAtPos(closestPlayer);
			}
		} else {
			// Kein Ziel gefunden - reset
			targetLockFrames = 0;
			lastTarget = Vec2(0, 0);
		}
	} else {
		// Taste nicht gedrückt - reset
		targetLockFrames = 0;
		lastTarget = Vec2(0, 0);
	}
}

float Aimbot::DistanceBetweenCross(float X, float Y)
{
    float ydist = (Y - ScreenCenterY);
    float xdist = (X - ScreenCenterX);
    float Hypotenuse = sqrt(pow(ydist, 2) + pow(xdist, 2));
    return Hypotenuse;
}

Vec2 Aimbot::GetClosestPlayerToCrosshair(const std::vector<Vector> playerPositions, float &Max)
{
    Vec2 Aim;
    float closestDistance = Max;
    bool foundTarget = false;

    // Intelligente Zielauswahl - verhindert Zittern zwischen Zielen
    for (const auto& pos : playerPositions) {
        float Distance = DistanceBetweenCross(pos.x, pos.y);

        // Nur Ziele innerhalb des Radius berücksichtigen
        if (Distance < Max) {
            // Wenn wir noch kein Ziel haben, nimm das erste gültige
            if (!foundTarget) {
                closestDistance = Distance;
                Aim.x = pos.x;
                Aim.y = pos.y;
                foundTarget = true;
            }
            // Wenn wir bereits ein Ziel haben, nur wechseln wenn das neue deutlich näher ist
            // Das verhindert Zittern zwischen ähnlich entfernten Zielen
            else if (Distance < closestDistance * 0.8f) { // 20% näher muss es sein
                closestDistance = Distance;
                Aim.x = pos.x;
                Aim.y = pos.y;
            }
        }
    }

    // Update Max nur wenn wir ein Ziel gefunden haben
    if (foundTarget) {
        Max = closestDistance;
    }

    return Aim;
}

void Aimbot::AimAtPos(Vec2 Pos)
{
    // Berechne die Distanz vom Crosshair zum Ziel
    float deltaX = Pos.x - ScreenCenterX;
    float deltaY = Pos.y - ScreenCenterY;

    // Berechne die Gesamtdistanz für intelligente Dämpfung
    float totalDistance = sqrt(deltaX * deltaX + deltaY * deltaY);

    // Wenn das Ziel zu nah am Crosshair ist, nicht bewegen (verhindert Zittern)
    if (totalDistance < 1.5f) {
        return;
    }

    // Intelligente Geschwindigkeitsberechnung basierend auf Distanz
    float aimSpeed = globals::Legitbot::smoothness;

    // Exponential falloff für sehr präzise Bewegungen bei kurzen Distanzen
    if (totalDistance < 20.0f) {
        aimSpeed *= (1.0f + (20.0f - totalDistance) / 20.0f); // Langsamere Bewegung bei kurzen Distanzen
    }

    // Basis-Bewegung berechnen
    float moveX = deltaX / aimSpeed;
    float moveY = deltaY / aimSpeed;

    // Screen boundary checks - verhindere Bewegung außerhalb des Bildschirms
    float futureX = ScreenCenterX + moveX;
    float futureY = ScreenCenterY + moveY;

    if (futureX < 10 || futureX > (ScreenCenterX * 2) - 10) {
        moveX *= 0.5f; // Reduziere Bewegung statt komplett zu stoppen
    }
    if (futureY < 10 || futureY > (ScreenCenterY * 2) - 10) {
        moveY *= 0.5f; // Reduziere Bewegung statt komplett zu stoppen
    }

    if (!Smooth)
    {
        // Direkte Bewegung ohne Smoothing - aber immer noch mit Präzision
        if (totalDistance > 5.0f) {
            mouse_event(0x0001, (UINT)(moveX), (UINT)(moveY), NULL, NULL);
        }
        return;
    }
    else
    {
        // Intelligentes Smoothing mit progressiver Dämpfung
        float smoothFactor = globals::Legitbot::smoothness;

        // Progressive Smoothing basierend auf Distanz
        if (totalDistance < 10.0f) {
            smoothFactor *= 2.0f; // Sehr viel Smoothing bei sehr kurzen Distanzen
        } else if (totalDistance < 30.0f) {
            smoothFactor *= 1.5f; // Mehr Smoothing bei kurzen Distanzen
        }

        moveX /= smoothFactor;
        moveY /= smoothFactor;

        // Intelligente Minimum-Bewegung mit Deadzone
        float minMove = 0.8f;

        if (abs(moveX) > 0.1f && abs(moveX) < minMove) {
            moveX = (moveX > 0) ? minMove : -minMove;
        }
        if (abs(moveY) > 0.1f && abs(moveY) < minMove) {
            moveY = (moveY > 0) ? minMove : -minMove;
        }

        // Sehr kleine Bewegungen ignorieren (verhindert Mikro-Zittern)
        if (abs(moveX) < 0.1f) moveX = 0;
        if (abs(moveY) < 0.1f) moveY = 0;

        // Nur bewegen wenn die Bewegung sinnvoll ist
        if (moveX != 0 || moveY != 0) {
            mouse_event(0x0001, (UINT)moveX, (UINT)moveY, NULL, NULL);
        }
        return;
    }
}