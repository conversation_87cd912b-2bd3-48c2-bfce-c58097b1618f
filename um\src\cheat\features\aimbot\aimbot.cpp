#include "pch.h"

#include "aimbot.hpp"
#include "../../entity.hpp"
#include "../../globals.hpp"
#include "../../gamedata.hpp"
#include <vector>
#include <cmath>
#include <Windows.h>

// ===========================
// PROFESSIONAL AIMBOT IMPLEMENTATION
// ===========================

bool Aimbot::Initialize()
{
	if (!mouseInitialized) {
		mouseInitialized = mouse_open();
		if (!mouseInitialized) {
			// Fallback: try again
			Sleep(100);
			mouseInitialized = mouse_open();
		}
	}
	return mouseInitialized;
}

void Aimbot::Shutdown()
{
	if (mouseInitialized) {
		mouse_close();
		mouseInitialized = false;
	}
}

void Aimbot::doAimbot(const Reader& reader)
{
	// Initialize mouse system if not done yet
	if (!mouseInitialized) {
		Initialize();
	}

	view_matrix_t viewMatrix = GameData::getViewMatrix();
	auto playerList = reader.getPlayerListCopy();

	std::vector<Vector> playerPositions;
	playerPositions.clear();

	for (const auto& player : playerList)
	{
		// get the 3D position of the player we're CURRENTLY looping through.
		Vector playerPosition = driver::read_memory<Vector>(GameVars::getInstance()->getDriver(), player.BoneArray + bones::head * 32);

		int localTeam = GameData::getLocalTeam();
		if (player.team == localTeam && globals::Legitbot::teamcheck)
			continue;

		Vector h;
		if (Vector::world_to_screen(viewMatrix, playerPosition, h))
		{
			playerPositions.push_back(h);
		}
	}

	if (GetAsyncKeyState(0x58)) // X on keyboard
	{
		float maxDistance = globals::Legitbot::radius;
		Vec2 closestPlayer = GetClosestPlayerToCrosshair(playerPositions, maxDistance);

		// Professional target selection with FOV check
		if (closestPlayer.x != 0 || closestPlayer.y != 0)
		{
			Vec2 screenCenter = Vec2(ScreenCenterX, ScreenCenterY);
			float norm = CalculateNorm(closestPlayer, screenCenter);

			// Only aim if target is within FOV
			if (norm < AimFov) {
				// Target persistence check
				float distanceToLastTarget = sqrt(pow(closestPlayer.x - lastTarget.x, 2) + pow(closestPlayer.y - lastTarget.y, 2));

				if (distanceToLastTarget < 15.0f) {
					targetLockFrames++;
				} else {
					targetLockFrames = 0;
					lastTarget = closestPlayer;
				}

				// Aim with professional mouse system
				if (targetLockFrames >= 1) {
					AimAtPos(closestPlayer);
				}
			}
		} else {
			targetLockFrames = 0;
			lastTarget = Vec2(0, 0);
		}
	} else {
		targetLockFrames = 0;
		lastTarget = Vec2(0, 0);
	}
}

float Aimbot::DistanceBetweenCross(float X, float Y)
{
    float ydist = (Y - ScreenCenterY);
    float xdist = (X - ScreenCenterX);
    float Hypotenuse = sqrt(pow(ydist, 2) + pow(xdist, 2));
    return Hypotenuse;
}

Vec2 Aimbot::GetClosestPlayerToCrosshair(const std::vector<Vector> playerPositions, float &Max)
{
    Vec2 Aim;
    float closestDistance = Max;
    bool foundTarget = false;

    // Intelligente Zielauswahl - verhindert Zittern zwischen Zielen
    for (const auto& pos : playerPositions) {
        float Distance = DistanceBetweenCross(pos.x, pos.y);

        // Nur Ziele innerhalb des Radius berücksichtigen
        if (Distance < Max) {
            // Wenn wir noch kein Ziel haben, nimm das erste gültige
            if (!foundTarget) {
                closestDistance = Distance;
                Aim.x = pos.x;
                Aim.y = pos.y;
                foundTarget = true;
            }
            // Wenn wir bereits ein Ziel haben, nur wechseln wenn das neue deutlich näher ist
            // Das verhindert Zittern zwischen ähnlich entfernten Zielen
            else if (Distance < closestDistance * 0.8f) { // 20% näher muss es sein
                closestDistance = Distance;
                Aim.x = pos.x;
                Aim.y = pos.y;
            }
        }
    }

    // Update Max nur wenn wir ein Ziel gefunden haben
    if (foundTarget) {
        Max = closestDistance;
    }

    return Aim;
}

float Aimbot::CalculateNorm(Vec2 screenPos, Vec2 center)
{
    float deltaX = screenPos.x - center.x;
    float deltaY = screenPos.y - center.y;
    return sqrt(deltaX * deltaX + deltaY * deltaY);
}

void Aimbot::AimAtPos(Vec2 Pos)
{
    // Professional aimbot implementation based on your reference code
    float TargetX = 0.0f;
    float TargetY = 0.0f;
    float Smooth = globals::Legitbot::smoothness;

    // Calculate screen center
    int ScreenCenterX = static_cast<int>(this->ScreenCenterX);
    int ScreenCenterY = static_cast<int>(this->ScreenCenterY);

    // Calculate norm (distance from center) for FOV check
    Vec2 screenCenter = Vec2(ScreenCenterX, ScreenCenterY);
    float norm = CalculateNorm(Pos, screenCenter);

    // Only proceed if within FOV
    if (norm < AimFov)
    {
        // X-axis calculation (exactly like your reference)
        if (Pos.x != ScreenCenterX)
        {
            TargetX = (Pos.x > ScreenCenterX) ? -(ScreenCenterX - Pos.x) : Pos.x - ScreenCenterX;
            TargetX /= (Smooth != 0.0f ? Smooth : 1.5f);
            TargetX = (TargetX + ScreenCenterX > ScreenCenterX * 2 || TargetX + ScreenCenterX < 0) ? 0 : TargetX;
        }

        // Y-axis calculation (exactly like your reference)
        if (Pos.y != 0)
        {
            if (Pos.y != ScreenCenterY)
            {
                TargetY = (Pos.y > ScreenCenterY) ? -(ScreenCenterY - Pos.y) : Pos.y - ScreenCenterY;
                TargetY /= (Smooth != 0.0f ? Smooth : 1.5f);
                TargetY = (TargetY + ScreenCenterY > ScreenCenterY * 2 || TargetY + ScreenCenterY < 0) ? 0 : TargetY;
            }
        }

        if (!this->Smooth)
        {
            // Direct movement without smoothing using professional mouse system
            if (mouseInitialized && (TargetX != 0 || TargetY != 0)) {
                mouse_move(0, (char)TargetX, (char)TargetY, 0);
            }
            return;
        }

        // Dynamic AimSmooth based on distance (like your reference)
        float DistanceRatio = norm / AimFov; // Calculate the distance ratio
        float SpeedFactor = 1.0f + (1.0f - DistanceRatio); // Determine the speed factor based on the distance ratio
        TargetX /= (Smooth * SpeedFactor);
        TargetY /= (Smooth * SpeedFactor);

        // Recalculate with smoothing (like your reference - this is intentional duplication)
        if (Pos.x != ScreenCenterX)
        {
            TargetX = (Pos.x > ScreenCenterX) ? -(ScreenCenterX - Pos.x) : Pos.x - ScreenCenterX;
            TargetX /= (Smooth != 0.0f ? Smooth : 1.5f);
            TargetX = (TargetX + ScreenCenterX > ScreenCenterX * 2 || TargetX + ScreenCenterX < 0) ? 0 : TargetX;
        }

        if (Pos.y != 0)
        {
            if (Pos.y != ScreenCenterY)
            {
                TargetY = (Pos.y > ScreenCenterY) ? -(ScreenCenterY - Pos.y) : Pos.y - ScreenCenterY;
                TargetY /= (Smooth != 0.0f ? Smooth : 1.5f);
                TargetY = (TargetY + ScreenCenterY > ScreenCenterY * 2 || TargetY + ScreenCenterY < 0) ? 0 : TargetY;
            }
        }

        // Use professional mouse system for movement
        if (mouseInitialized && (TargetX != 0 || TargetY != 0)) {
            mouse_move(0, (char)TargetX, (char)TargetY, 0);
        }
    }
}