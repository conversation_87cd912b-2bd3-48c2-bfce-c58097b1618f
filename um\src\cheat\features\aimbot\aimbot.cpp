#include "pch.h"

#include "aimbot.hpp"
#include "../../entity.hpp"
#include "../../globals.hpp"
#include "../../gamedata.hpp"
#include <vector>
#include <cmath>
#include <Windows.h>

// ===========================
// AIMBOT IMPLEMENTATION
// ===========================

void Aimbot::doAimbot(const Reader& reader)
{
	view_matrix_t viewMatrix = GameData::getViewMatrix();
	auto playerList = reader.getPlayerListCopy();

	std::vector<Vector> playerPositions;
	playerPositions.clear();

	for (const auto& player : playerList)
	{
		// get the 3D position of the player we're CURRENTLY looping through.
		Vector playerPosition = driver::read_memory<Vector>(GameVars::getInstance()->getDriver(), player.BoneArray + bones::head * 32);


		//if (!player.enemySpotted && player.playerTeam != localTeam && Legitbot.visiblecheck)
		//	continue;

		int localTeam = GameData::getLocalTeam();
		if (player.team == localTeam && globals::Legitbot::teamcheck)
			continue;


		Vector h;
		if (Vector::world_to_screen(viewMatrix, playerPosition, h))
		{
			playerPositions.push_back(h);
		}
	}

	if (GetAsyncKeyState(0x58)) // X on keyboard
	{
		float maxDistance = globals::Legitbot::radius;
		Vec2 closestPlayer = GetClosestPlayerToCrosshair(playerPositions, maxDistance);
		if (closestPlayer.x != 0 || closestPlayer.y != 0)
		{
			AimAtPos(closestPlayer);
		}
	}
}

float Aimbot::DistanceBetweenCross(float X, float Y)
{
    float ydist = (Y - ScreenCenterY);
    float xdist = (X - ScreenCenterX);
    float Hypotenuse = sqrt(pow(ydist, 2) + pow(xdist, 2));
    return Hypotenuse;
}

Vec2 Aimbot::GetClosestPlayerToCrosshair(const std::vector<Vector> playerPositions, float &Max)
{
    Vec2 Aim;
    for (const auto& pos : playerPositions) {
        float Distance = DistanceBetweenCross(pos.x, pos.y);
        if (Distance < Max)
        {
           Max = Distance;
           Aim.x = pos.x;
           Aim.y = pos.y;
        }
    }
    return Aim;
}

void Aimbot::AimAtPos(Vec2 Pos)
{
    float TargetX = 0;
    float TargetY = 0;
    if (Pos.x != 0)
    {
        if (Pos.x > ScreenCenterX)
        {
            TargetX = -(ScreenCenterX - Pos.x);
            TargetX /= globals::Legitbot::smoothness; // Using smoothness as AimSpeed
            if (TargetX + ScreenCenterX > ScreenCenterX * 2) TargetX = 0;
        }

        if (Pos.x < ScreenCenterX)
        {
            TargetX = Pos.x - ScreenCenterX;
            TargetX /= globals::Legitbot::smoothness; // Using smoothness as AimSpeed
            if (TargetX + ScreenCenterX < 0) TargetX = 0;
        }
    }
    if (Pos.y != 0)
    {
        if (Pos.y > ScreenCenterY)
        {
            TargetY = -(ScreenCenterY - Pos.y);
            TargetY /= globals::Legitbot::smoothness; // Using smoothness as AimSpeed
                if (TargetY + ScreenCenterY > ScreenCenterY * 2) TargetY = 0;
        }

        if (Pos.y < ScreenCenterY)
        {
            TargetY = Pos.y - ScreenCenterY;
            TargetY /= globals::Legitbot::smoothness; // Using smoothness as AimSpeed
            if (TargetY + ScreenCenterY < 0) TargetY = 0;
        }
    }
    if (!Smooth)
    {
        mouse_event(0x0001, (UINT)(TargetX), (UINT)(TargetY), NULL, NULL);
        return;
    }
    else
    {
        TargetX /= globals::Legitbot::smoothness; // Using smoothness as SmoothAmount
        TargetY /= globals::Legitbot::smoothness; // Using smoothness as SmoothAmount
        if (abs(TargetX) < 1)
        {
            if (TargetX > 0)
            {
                TargetX = 1;
            }
            if (TargetX < 0)
            {
                TargetX = -1;
            }
        }
        if (abs(TargetY) < 1)
        {
            if (TargetY > 0)
            {
                TargetY = 1;
            }
            if (TargetY < 0)
            {
                TargetY = -1;
            }
        }
        mouse_event(0x0001, (UINT)TargetX, (UINT)TargetY, NULL, NULL);
        return;
    }
}